/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.api.table.schema.event;

import org.apache.seatunnel.api.event.EventType;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;

import lombok.Getter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@Getter
@ToString(callSuper = true)
public class AlterTableColumnsEvent extends AlterTableEvent {
    private final List<AlterTableColumnEvent> events;

    public AlterTableColumnsEvent(TableIdentifier tableIdentifier) {
        this(tableIdentifier, new ArrayList<>());
    }

    public AlterTableColumnsEvent(
            TableIdentifier tableIdentifier, List<AlterTableColumnEvent> events) {
        super(tableIdentifier);
        this.events = events;
    }

    public AlterTableColumnsEvent addEvent(AlterTableColumnEvent event) {
        events.add(event);
        return this;
    }

    @Override
    public EventType getEventType() {
        return EventType.SCHEMA_CHANGE_UPDATE_COLUMNS;
    }
}
