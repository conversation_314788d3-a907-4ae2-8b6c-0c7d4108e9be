#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  # You can set engine configuration here
  execution.parallelism = 1
  job.mode = "STREAMING"
  checkpoint.interval = 5000
  read_limit.bytes_per_second=7000000
  read_limit.rows_per_second=400
}

source {
  Opengauss-CDC {
    plugin_output = "customers_opengauss_cdc"
    username = "gaussdb"
    password = "openGauss@123"
    database-names = ["opengauss_cdc"]
    schema-names = ["inventory"]
    table-names = ["opengauss_cdc.inventory.opengauss_cdc_table_1"]
    base-url = "**********************************************************************"
    decoding.plugin.name = "pgoutput"
  }
}

transform {
  Metadata {
    metadata_fields {
      Database = database
      Table = table
      RowKind = rowKind
      EventTime = ts_ms
      Delay = delay
    }
    plugin_output = "trans_result"
  }
}

sink {
  Assert {
    plugin_input = "trans_result"
    rules {
      field_rules = [
        {
          field_name = database
          field_type = string
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        }, {
          field_name = table
          field_type = string
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        }, {
          field_name = rowKind
          field_type = string
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        }, {
          field_name = ts_ms
          field_type = long
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        }, {
          field_name = delay
          field_type = long
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        }
      ]
    }
  }
}
