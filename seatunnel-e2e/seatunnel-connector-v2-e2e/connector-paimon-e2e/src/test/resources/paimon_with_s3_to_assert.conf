#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  execution.parallelism = 1
  spark.app.name = "SeaTunnel"
  spark.executor.instances = 2
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.master = local
  job.mode = "BATCH"
}

source {
  Paimon {
    warehouse = "s3a://test/"
    database = "seatunnel_namespace11"
    table = "st_test"
    paimon.hadoop.conf = {
        fs.s3a.access-key=minio
        fs.s3a.secret-key=miniominio
        fs.s3a.endpoint="http://minio:9000"
        fs.s3a.path.style.access=true
        fs.s3a.aws.credentials.provider=org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider
    }
  }
}

sink {
 Assert {
    rules {
        row_rules = [
            {
              rule_type = MIN_ROW
              rule_value = 2
            }
          ],
          row_rules = [
            {
              rule_type = MAX_ROW
              rule_value = 2
            }
          ],
          field_rules = [
            {
              field_name = pk_id
              field_type = bigint
              field_value = [
                {
                  rule_type = NOT_NULL
                },
                {
                  rule_type = MIN
                  rule_value = 1
                },
                {
                  rule_type = MAX
                  rule_value = 3
                }
              ]
            },
            {
              field_name = name
              field_type = string
              field_value = [
                {
                  rule_type = NOT_NULL
                }
              ]
            },
             {
               field_name = score
               field_type = int
               field_value = [
                 {
                   rule_type = NOT_NULL
                   equals_to = 100
                 }
               ]
             }
          ]
        }
  }
}
