#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  FakeSource {
    schema = {
      columns = [
         {
            name = pk_id
            type = bigint
            nullable = false
            comment = "primary key id"
         },
         {
            name = name
            type = "string"
            nullable = true
            comment = "name"
         },
         {
            name = one_date
            type = date
            nullable = false
            comment = "one date"
         }
      ]
      primaryKey {
        name = "pk_id"
        columnNames = [pk_id]
      }
    }
    rows = [
      {
        kind = INSERT
        fields = [1, "A", "2024-03-10"]
      },
      {
        kind = INSERT
        fields = [2, "B", "2024-03-10"]
      },
      {
        kind = INSERT
        fields = [3, "C", "2024-03-10"]
      },
      {
        kind = INSERT
        fields = [3, "C", "2024-03-10"]
      },
      {
        kind = UPDATE_BEFORE
        fields = [1, "A", "2024-03-10"]
      },
      {
        kind = UPDATE_AFTER
        fields = [1, "A_1", "2024-03-20"]
      }
    ]
  }
}

transform {

}

sink {
  Paimon {
    warehouse = "file:///tmp/paimon"
    database = "seatunnel_namespace8"
    table = "st_test"
  }
}
