#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  Http {
    plugin_output = "http"
    url = "http://mockserver:1080/query/pages"
    method = "GET"
    format = "json"
    json_field = {
      name = "$.data[*].name"
      age = "$.data[*].age"
    }
    keep_page_param_as_http_param = true
    pageing = {
      total_page_size = 2
      page_field = page
      start_page_number = 2
    }
    schema = {
      fields {
        name = string
        age = int
      }
    }
  }
}

sink {
  Assert {
    plugin_input = "http"
    rules {
      row_rules = [
        {
          rule_type = MIN_ROW
          rule_value = 2
        },
        {
          rule_type = MAX_ROW
          rule_value = 2
        }
      ]
      field_rules = [
        {
          field_name = name
          field_type = string
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        },
        {
          field_name = age
          field_type = int
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        }
      ]
    }
  }
}
