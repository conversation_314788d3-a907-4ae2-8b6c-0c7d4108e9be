#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
    parallelism = 1
    job.mode = "BATCH"
    flink.execution.checkpointing.interval=5000
     flink.execution.restart.strategy = failure-rate
     flink.execution.restart.failureInterval = 60000
     flink.execution.restart.failureRate = 100
     flink.execution.restart.delayInterval = 10000

}
source {
   Typesense {
      hosts = ["e2e_typesense:8108"]
      collection = "typesense_to_typesense_source"
      api_key = "xyz"
      plugin_output = "typesense_test_table"
      schema = {
            fields {
              company_name_list = array<string>
              company_name = string
              num_employees = long
              country = string
              id = string
              c_row = {
                c_int = int
                c_string = string
                c_array_int = array<int>
              }
            }
          }
    }
}

sink {
    Typesense {
        plugin_input = "typesense_test_table"
        hosts = ["e2e_typesense:8108"]
        collection = "typesense_to_typesense_sink"
        max_retry_count = 3
        max_batch_size = 10
        api_key = "xyz"
        primary_keys = ["num_employees","id"]
        key_delimiter = "="
        schema_save_mode = "CREATE_SCHEMA_WHEN_NOT_EXIST"
        data_save_mode = "APPEND_DATA"
      }
}