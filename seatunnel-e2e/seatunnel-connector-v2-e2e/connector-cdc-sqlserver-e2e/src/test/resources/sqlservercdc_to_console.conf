#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  # You can set engine configuration here
  parallelism = 1
  job.mode = "STREAMING"
  checkpoint.interval = 5000
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
  SqlServer-CDC {
    plugin_output = "customers"
    username = "sa"
    password = "Password!"
    database-names = ["column_type_test"]
    table-names = ["column_type_test.dbo.full_types"]
    base-url = "******************************************************************"
  }
}

transform {
}

sink {
  Jdbc {
    plugin_input = "customers"
    driver = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
    url = "**************************************************"
    user = "sa"
    password = "Password!"
    generate_sink_sql = true
    database = "column_type_test"
    table = "dbo.full_types_sink"
    batch_size = 1
    primary_keys = ["id"]
  }
}