#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"

  # You can set spark configuration here
  spark.app.name = "SeaTunnel"
  spark.executor.instances = 1
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.master = local
}

source {
  FakeSource {
    plugin_output = "sftp"
    tables_configs = [
       {
        schema = {
          table = "source_1"
         fields {
                id = int
                val_bool = boolean
                val_tinyint = tinyint
                val_smallint = smallint
                val_int = int
                val_bigint = bigint
                val_float = float
                val_double = double
                val_decimal = "decimal(16, 1)"
                val_string = string
      }
        }
            rows = [
              {
                kind = INSERT
                fields = [1, true, 1, 2, 3, 4, 4.3,5.3,6.3, "NEW"]
              }
              ]
       },
       {
       schema = {
         table = "source_2"
              fields {
                id = int
                val_bool = boolean
                val_tinyint = tinyint
                val_smallint = smallint
                val_int = int
                val_bigint = bigint
                val_float = float
                val_double = double
                val_decimal = "decimal(16, 1)"
              }
       }
           rows = [
             {
               kind = INSERT
               fields = [1, true, 1, 2, 3, 4, 4.3,5.3,6.3]
             }
             ]
      }
    ]
  }
}


sink {
  SftpFile {
    host = "sftp"
    port = 22
    user = seatunnel
    password = pass
    path = "tmp/multiple_1/seatunnel/text/${table_name}"
    plugin_input = "sftp"
    row_delimiter = "\n"
    partition_dir_expression = "${k0}=${v0}"
    is_partition_field_write_in_file = true
    file_name_expression = "${transactionId}_${now}"
    file_format_type = "text"
    filename_time_format = "yyyy.MM.dd"
    is_enable_transaction = true
    compress_codec = "lzo"
    "schema_save_mode"="RECREATE_SCHEMA"
    "data_save_mode"="DROP_DATA"
  }
}