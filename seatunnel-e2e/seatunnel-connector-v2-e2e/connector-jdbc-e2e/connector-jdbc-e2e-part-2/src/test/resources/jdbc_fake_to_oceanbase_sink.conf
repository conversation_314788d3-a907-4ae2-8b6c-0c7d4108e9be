#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  FakeSource {
      row.num = 10
      vector.dimension= 4
      schema = {
           table = "simple_example_1"
           columns = [
           {
              name = book_id
              type = bigint
              nullable = false
              defaultValue = 0
              comment = "primary key id"
           },
           {
              name = book_intro
              type = float_vector
              columnScale =4
              comment = "vector"
           },
           {
              name = book_title
              type = string
              nullable = true
              comment = "topic"
           }
       ]
        primaryKey {
            name = book_id
            columnNames = [book_id]
        }
      }
  }
}

sink {
  jdbc {
    url = "****************************************************"
    driver = "com.oceanbase.jdbc.Driver"
    user = "root@test"
    password = ""
    generate_sink_sql =true
    compatible_mode="mysql"
    database = "seatunnel"
    table = "simple_example"
  }
}