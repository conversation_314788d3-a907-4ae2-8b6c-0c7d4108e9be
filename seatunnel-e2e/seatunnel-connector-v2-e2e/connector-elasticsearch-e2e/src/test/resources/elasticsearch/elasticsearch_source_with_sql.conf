#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"
  #checkpoint.interval = 10000
}

source {
  Elasticsearch {
    hosts = ["https://elasticsearch:9200"]
    username = "elastic"
    password = "elasticsearch"
    tls_verify_certificate = false
    tls_verify_hostname = false
    index = "st_index_sql"
    sql_query = "select * from st_index_sql where c_int>=10 and c_int<=20"
    search_type = "SQL"
  }
}

sink {
  Assert {
    rules {
      row_rules = [
        {
          rule_type = MAX_ROW
          rule_value = 1
        }
      ],
      field_rules = [
        {
          field_name = c_string
          field_type = string
          field_value = [
            {
              rule_type = NOT_NULL
              equals_to = "string"
            }
          ]
        },
        {
          field_name = c_boolean
          field_type = boolean
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        },
        {
          field_name = c_bytes
          field_type = string
          field_value = [
            {
              rule_type = NOT_NULL
              equals_to = "dGVzdA=="
            }
          ]
        },
        {
          field_name = c_decimal
          field_type = float
          field_value = [
            {
              rule_type = NOT_NULL
              equals_to = 1.1
            }
          ]
        },
        {
          field_name = c_double
          field_type = double
          field_value = [
            {
              rule_type = NOT_NULL
              equals_to = 1.1
            }
          ]
        },
        {
          field_name = c_float
          field_type = float
          field_value = [
            {
              rule_type = NOT_NULL
              equals_to = 1.1
            }
          ]
        },
        {
          field_name = c_int
          field_type = long
          field_value = [
            {
              rule_type = NOT_NULL
              equals_to = 10
            }
          ]
        },
        {
          field_name = c_timestamp
          field_type = long
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        },
        {
          field_name = c_tinyint
          field_type = long
          field_value = [
            {
              rule_type = NOT_NULL
              equals_to = 1
            }
          ]
        },
        {
          field_name = c_smallint
          field_type = long
          field_value = [
            {
              rule_type = NOT_NULL
              equals_to = 1
            }
          ]
        },
        {
          field_name = c_bigint
          field_type = long
          field_value = [
            {
              rule_type = NOT_NULL
              equals_to = 1
            }
          ]
        },
        {
          field_name = c_date
          field_type = timestamp
          field_value = [
            {
              rule_type = NOT_NULL
              equals_to = "2025-03-03 00:00:00"
            }
          ]
        }
      ]
    }
  }
}