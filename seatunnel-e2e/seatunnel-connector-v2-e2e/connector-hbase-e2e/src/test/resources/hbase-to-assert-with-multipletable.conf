#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  Hbase {
      zookeeper_quorum = "hbase_e2e:2181"
      table = "seatunnel_test"
      query_columns=["rowkey", "info:age", "info:c_double", "info:c_boolean","info:c_bigint","info:c_smallint","info:c_tinyint","info:c_float"]
      schema = {
            columns = [
                  {
                     name = rowkey
                     type = string
                  },
                  {
                     name = "info:age"
                     type = int
                  },
                  {
                     name = "info:c_double"
                     type = double
                  },
                  {
                     name = "info:c_boolean"
                     type = boolean
                  },
                  {
                     name = "info:c_bigint"
                     type = bigint
                  },
                  {
                     name = "info:c_smallint"
                     type = smallint
                  },
                  {
                     name = "info:c_tinyint"
                     type = tinyint
                  },
                  {
                     name = "info:c_float"
                     type = float
                  }
             ]
       }
    }
}

sink {
  Assert {
    rules {
      row_rules = [
        {
          rule_type = MAX_ROW
          rule_value = 11
        },
        {
          rule_type = MIN_ROW
          rule_value = 11
        }
      ],
      field_rules = [
        {
          field_name = rowkey
          field_type = string
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        },
        {
          field_name = "info:c_boolean"
          field_type = boolean
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        },
        {
          field_name = "info:c_double"
          field_type = double
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        },
        {
          field_name = "info:c_bigint"
          field_type = bigint
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        },
        {
          field_name = "info:age"
          field_type = int
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        }
      ]
    }
  }
}