#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  execution.parallelism = 1
  spark.app.name = "SeaTunnel"
  spark.executor.instances = 2
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.master = local
  job.mode = "BATCH"
}

source {
  OssFile {
    bucket = "oss://whale-ops"
    access_key = "xxxxxxxxxxxxxxxxxxx"
    access_secret = "xxxxxxxxxxxxxxxxxxx"
    endpoint = "https://oss-accelerate.aliyuncs.com"
    plugin_output = "fake"
    path = "/test/seatunnel/read/lzo_json"
    row_delimiter = "\n"
    partition_dir_expression = "${k0}=${v0}"
    is_partition_field_write_in_file = true
    file_name_expression = "${transactionId}_${now}"
    file_format_type = "json"
    compress_codec = "lzo"
    filename_time_format = "yyyy.MM.dd"
    is_enable_transaction = true
    schema = {
      fields {
        c_map = "map<string, string>"
        c_array = "array<int>"
        c_string = string
        c_boolean = boolean
        c_tinyint = tinyint
        c_smallint = smallint
        c_int = int
        c_bigint = bigint
        c_float = float
        c_double = double
        c_bytes = bytes
        c_date = date
        c_decimal = "decimal(38, 18)"
        c_timestamp = timestamp
        c_row = {
          C_MAP = "map<string, string>"
          C_ARRAY = "array<int>"
          C_STRING = string
          C_BOOLEAN = boolean
          C_TINYINT = tinyint
          C_SMALLINT = smallint
          C_INT = int
          C_BIGINT = bigint
          C_FLOAT = float
          C_DOUBLE = double
          C_BYTES = bytes
          C_DATE = date
          C_DECIMAL = "decimal(38, 18)"
          C_TIMESTAMP = timestamp
        }
      }
    }
  }
}

transform {
  sql {
    plugin_input = "fake"
    plugin_output = "sqlresult"
    query = "select * from dual where c_string = 'WArEB'"
  }
}

sink {
  Assert {
    plugin_input = "sqlresult"
    rules {
      row_rules = [
        {
          rule_type = MAX_ROW
          rule_value = 1
        },
        {
          rule_type = MIN_ROW
          rule_value = 1
        }
      ],
      field_rules = [
        {
          field_name = c_string
          field_type = string
          field_value = [
            {
              equals_to = "WArEB"
            }
          ]
        },
        {
          field_name = c_boolean
          field_type = boolean
          field_value = [
            {
              rule_type = NOT_NULL
            }
          ]
        },
        {
          field_name = c_smallint
          field_type = smallint
          field_value = [
            {
              equals_to = 15920
            }
          ]
        },
        {
          field_name = c_date
          field_type = date
          field_value = [
            {
              equals_to = "2022-04-27"
            }
          ]
        }
      ]
    }
  }
}