#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  parallelism = 1
  job.mode = "BATCH"

  # You can set spark configuration here
  spark.app.name = "SeaTunnel"
  spark.executor.instances = 2
  spark.executor.cores = 1
  spark.executor.memory = "1g"
  spark.driver.extraJavaOptions = "-Dfile.encoding=UTF-8"
  spark.master = local
}

source {
  FakeSource {
    schema = {
      fields {
        c_map = "map<string, string>"
        c_array = "array<int>"
        c_array_string = "array<string>"
        c_string = string
        c_boolean = boolean
        c_tinyint = tinyint
        c_smallint = smallint
        c_int = int
        c_bigint = bigint
        c_float = float
        c_double = double
        c_decimal = "decimal(30, 8)"
        c_null = "null"
        c_bytes = bytes
        c_date = date
        c_timestamp = timestamp
      }
    }
    rows = [
      {
        kind = INSERT
        fields = [{"aA\"测试\"": "bB\"测试\""}, [101], ["测试ABC123!@#"], "\"你好，世界\"ABC123!@#", true, 117, 15987, 56387395, 7084913402530365000, 1.23, 1.23, "2924137191386439303744.39292216", null, "5L2g5aW95LiW55WMQUJDYWJjMTIzIUAj", "2023-04-22", "2023-04-22T23:20:58"]
      }
      {
        kind = UPDATE_BEFORE
        fields = [{"aA\"测试\"": "c"}, [102], ["\"测试\"ABC123!@#"], "\"海底隧道\"", true, 117, 15987, 56387395, 7084913402530365000, 1.23, 1.23, "2924137191386439303744.39292216", null, "5L2g5aW95LiW55WMQUJDYWJjMTIzIUAj", "2023-04-22", "2023-04-22T23:20:58"]
      }
      {
        kind = UPDATE_AFTER
        fields = [{"a": "eE\"测试\""}, [103], ["\"测试\"ABC123!@#"], "GBK\"字符﨎\"", true, 117, 15987, 56387395, 7084913402530365000, 1.23, 1.23, "2924137191386439303744.39292216", null, "5L2g5aW95LiW55WMQUJDYWJjMTIzIUAj", "2023-04-22", "2023-04-22T23:20:58"]
      }
      {
        kind = DELETE
        fields = [{"a": "f"}, [104], ["\"测试\"ABC123!@#"], "\"测试字符\"", true, 117, 15987, 56387395, 7084913402530365000, 1.23, 1.23, "2924137191386439303744.39292216", null, "5L2g5aW95LiW55WMQUJDYWJjMTIzIUAj", "2023-04-22", "2023-04-22T23:20:58"]
      }
    ]
  }
}

transform {
}

sink {
  LocalFile {
    path = "/tmp/seatunnel/encoding/json"
    file_format_type = "json"
    encoding = "gbk"
  }
}