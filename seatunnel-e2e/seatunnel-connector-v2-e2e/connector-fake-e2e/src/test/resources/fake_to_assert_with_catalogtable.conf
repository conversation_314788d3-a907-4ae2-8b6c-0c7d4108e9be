#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

env {
  parallelism = 1
  job.mode = "BATCH"
}

source {
  FakeSource {
    row.num = 100
    schema = {
      table = "test.fakeTable"
      columns = [
        {
            name = id
            type = bigint
        }
        {
            name = name
            type = string
        }
        {
            name = age
            type = int
        }
      ]
      primaryKey = {
        name = "primary key"
        columnNames = ["id"]
      }
      constraintKeys = [
          {
              constraintName = "unique_name"
              constraintType = UNIQUE_KEY
              constraintColumns = [
                  {
                      columnName = "id"
                      sortType = ASC
                  }
              ]
          }
      ]
    }
    plugin_output = "fake"
  }
}

sink{
  Assert {
      rules {
        catalog_table_rule {
            table_identifier_rule = {
                catalog_name = "FakeSource"
                table = "test.fakeTable"
            }

            primary_key_rule = {
                primary_key_name = "primary key"
                primary_key_columns = ["id"]
            }
            constraint_key_rule = [
                {
                constraint_key_name = "unique_name"
                constraint_key_type = UNIQUE_KEY
                constraint_key_columns = [
                    {
                        constraint_key_column_name = "id"
                        constraint_key_sort_type = ASC
                    }
                ]
                }
            ]
            column_rule = [
               {
                name = "id"
                type = bigint
               },
              {
                name = "name"
                type = string
              },
              {
                name = "age"
                type = int
              }
            ]
        }
      }
    }
}