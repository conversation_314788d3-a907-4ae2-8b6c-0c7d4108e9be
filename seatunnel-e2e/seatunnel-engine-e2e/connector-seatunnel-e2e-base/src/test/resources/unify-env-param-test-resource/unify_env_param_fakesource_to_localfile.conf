#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  # You can set common configuration here
  job.name = "FLINK_ENV_PARAM_UNIFY"
  job.mode = "STREAMING"
  parallelism = 1
  checkpoint.interval = 10000
  # flink env
  flink.pipeline.time-characteristic = "ProcessingTime"
  flink.execution.buffer-timeout = 100
  flink.pipeline.max-parallelism = 5
  flink.execution.checkpointing.mode = "EXACTLY_ONCE"
  flink.execution.checkpointing.timeout = 600000
  flink.execution.checkpointing.min-pause = 100
  flink.execution.checkpointing.max-concurrent-checkpoints = 2
  flink.execution.checkpointing.externalized-checkpoint-retention = "DELETE_ON_CANCELLATION"
  flink.execution.checkpointing.tolerable-failed-checkpoints = 5
  flink.restart-strategy = "fixed-delay"
  flink.restart-strategy.fixed-delay.attempts = 2
  flink.restart-strategy.fixed-delay.delay = 1000
  flink.state.backend = "rocksdb"
  flink.state.checkpoints.dir = "file:///tmp/seatunnel/flink/checkpoints/"
}

source {
  # This is a example source plugin **only for test and demonstrate the feature source plugin**
  FakeSource {
    plugin_output = "fake"
    row.num = 100
    split.num = 5
    split.read-interval = 3000
    schema = {
      fields {
        id = "int"
        name = "string"
        age = "int"
      }
    }
  }
}

transform {
}

sink {
  LocalFile {
    path = "/tmp/seatunnel/config/unify-env-param-test-resource/sinkfile/"
    row_delimiter = "\n"
    file_name_expression = "${transactionId}_${now}"
    file_format_type = "text"
    filename_time_format = "yyyy.MM.dd"
    is_enable_transaction = true
  }
}