#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  job.mode = "BATCH"
}

source {
  FakeSource {
    plugin_output = "fake"
    row.num = 100
    schema = {
      fields {
        id = "int"
        name = "string"
      }
    }
  }
}

transform {
 DynamicCompile {
    plugin_input = "fake"
    plugin_output = "fake1"
    compile_language="GROOVY"
    compile_pattern="SOURCE_CODE"
    source_code="""
                 import org.apache.seatunnel.api.table.catalog.Column
                 import org.apache.seatunnel.api.table.type.SeaTunnelRowAccessor
                 import org.apache.seatunnel.api.table.catalog.CatalogTable
                 import org.apache.seatunnel.api.table.catalog.PhysicalColumn;
                 import org.apache.seatunnel.api.table.type.*;
                 import java.util.ArrayList;
                 class demo  {
                     public Column[] getInlineOutputColumns(CatalogTable inputCatalogTable) {
                          List<Column> columns = new ArrayList<>();
                         PhysicalColumn destColumn =
                         PhysicalColumn.of(
                         "aa",
                        BasicType.STRING_TYPE,
                         10,
                        true,
                        "",
                        "");
                         columns.add(destColumn);
                        return columns.toArray(new Column[0]);
                     }
                     public Object[] getInlineOutputFieldValues(SeaTunnelRowAccessor inputRow) {
                       Object[] fieldValues = new Object[1];
                       fieldValues[0]="AA"
                       return fieldValues;
                     }
                 };"""

  }
  DynamicCompile {
      plugin_input = "fake1"
      plugin_output = "fake2"
      compile_language="GROOVY"
      compile_pattern="SOURCE_CODE"
      source_code="""
                   import org.apache.seatunnel.api.table.catalog.Column
                   import org.apache.seatunnel.api.table.type.SeaTunnelRowAccessor
                   import org.apache.seatunnel.api.table.catalog.CatalogTable
                   import org.apache.seatunnel.api.table.catalog.PhysicalColumn;
                   import org.apache.seatunnel.api.table.type.*;
                   import java.util.ArrayList;
                   class demo  {
                       public Column[] getInlineOutputColumns(CatalogTable inputCatalogTable) {
                            List<Column> columns = new ArrayList<>();
                           PhysicalColumn destColumn =
                           PhysicalColumn.of(
                           "bb",
                          BasicType.STRING_TYPE,
                           10,
                          true,
                          "",
                          "");
                           columns.add(destColumn);
                          return columns.toArray(new Column[0]);
                       }
                       public Object[] getInlineOutputFieldValues(SeaTunnelRowAccessor inputRow) {
                         Object[] fieldValues = new Object[1];
                         fieldValues[0]="BB"
                         return fieldValues;
                       }
                   };"""

    }
}

sink {
  Assert {
    plugin_input = "fake2"
    rules =
      {
        row_rules = [
          {
            rule_type = MIN_ROW
            rule_value = 100
          }
        ],
        field_rules = [
          {
            field_name = id
            field_type = int
            field_value = [
              {
                rule_type = NOT_NULL
              }
            ]
          },
           {
                      field_name = bb
                      field_type = string
                      field_value = [
                        {
                          rule_type = NOT_NULL
                          equals_to = "BB"

                        }

                      ]
                    }
          {
            field_name = aa
            field_type = string
            field_value = [
              {
                rule_type = NOT_NULL
                equals_to = "AA"

              }

            ]
          }
        ]
      }
  }
}
