#
 # Licensed to the Apache Software Foundation (ASF) under one or more
 # contributor license agreements.  See the NOTICE file distributed with
 # this work for additional information regarding copyright ownership.
 # The ASF licenses this file to You under the Apache License, Version 2.0
 # (the "License"); you may not use this file except in compliance with
 # the License.  You may obtain a copy of the License at
 #
 #    http://www.apache.org/licenses/LICENSE-2.0
 #
 # Unless required by applicable law or agreed to in writing, software
 # distributed under the License is distributed on an "AS IS" BASIS,
 # WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 # See the License for the specific language governing permissions and
 # limitations under the License.
 #
 ######
 ###### This config file is a demonstration of streaming processing in seatunnel config
 ######
 env {
   job.mode = "BATCH"
 }

 source {
   FakeSource {
     plugin_output = "fake"
     row.num = 100
     string.fake.mode = "template"
     string.template=["{"data":{"c_map_string_array":[{"c_string_1":"c_string_1","c_string_2":"c_string_2","c_string_3":"c_string_3"},{"c_string_1":"c_string_1","c_string_2":"c_string_2","c_string_3":"c_string_3"}],"c_map_int_array":[{"c_int_1":1,"c_int_2":2,"c_int_3":3},{"c_int_1":1,"c_int_2":2,"c_int_3":3}]}}"]
     schema = {
       fields {
         data = "string"
       }
     }
   }
 }

 transform {
   JsonPath {
     plugin_input = "fake"
     plugin_output = "fake1"
     columns = [
      {
         "src_field" = "data"
         "path" = "$.data.c_map_string_array"
         "dest_field" = "c_map_string_array_1"
         "dest_type" = "array<map<string, string>>"
      },
     {
        "src_field" = "data"
        "path" = "$.data.c_map_int_array"
        "dest_field" = "c_map_int_array_1"
        "dest_type" = "array<map<string, int>>"
     }
     ]
   }
     Sql {
     plugin_input = "fake1"
     plugin_output = "fake2"
       query = "select c_map_string_array_1,c_map_int_array_1 from dual"
     }
 }

 sink {
   Assert {
     plugin_input = "fake2"
     rules =
       {
         row_rules = [
           {
             rule_type = MIN_ROW
             rule_value = 100
           }
         ],
         field_rules = [
           {
             field_name = c_map_string_array_1
             field_type = "array<map<string, string>>"
             field_value = [
               {
                 rule_type = NOT_NULL
                 equals_to = [{c_string_1=c_string_1, c_string_2=c_string_2, c_string_3=c_string_3}, {c_string_1=c_string_1, c_string_2=c_string_2, c_string_3=c_string_3}]
               }
             ]
           },
           {
             field_name = c_map_int_array_1
             field_type = "array<map<string, int>>"
             field_value = [
               {
                 rule_type = NOT_NULL
                 equals_to = [{c_int_1=1, c_int_2=2, c_int_3=3}, {c_int_1=1, c_int_2=2, c_int_3=3}]
               }
             ]
           }
         ]
       }
   }
 }