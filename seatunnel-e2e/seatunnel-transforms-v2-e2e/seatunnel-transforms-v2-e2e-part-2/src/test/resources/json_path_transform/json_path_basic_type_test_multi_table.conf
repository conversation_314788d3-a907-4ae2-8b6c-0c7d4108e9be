#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  job.mode = "BATCH"
}

source {
  FakeSource {
    tables_configs = [
      {
        row.num = 100
        string.fake.mode = "template"
        string.template=["{"data":{"c_string": "this is a string","c_boolean": "true","c_integer": "42","c_float": "3.14","c_double": "3.14","c_decimal": "10.55","c_date":"'2023-10-29'","c_datetime":\"16:12:43.459\"}}"]
        schema = {
          table = "test.abc"
          columns = [
            {
              name = "id"
              type = "bigint"
            },
            {
              name = "name"
              type = "string"
            },
            {
              name = "age"
              type = "int"
            }
          ]
        }
      },
      {
        row.num = 100
        string.fake.mode = "template"
        string.template=["{"data":{"c_string": "this is a string","c_boolean": "true","c_integer": "42","c_float": "3.14","c_double": "3.14","c_decimal": "10.55","c_date":"'2023-10-29'","c_datetime":\"16:12:43.459\"}}"]
        schema = {
          table = "test.xyz"
          columns = [
            {
              name = "id"
              type = "bigint"
            },
            {
              name = "name"
              type = "string"
            },
            {
              name = "age"
              type = "int"
            }
          ]
        }
      },
      {
        row.num = 100
        string.fake.mode = "template"
        string.template=["{"data":{"c_string": "this is a string","c_boolean": "true","c_integer": "42","c_float": "3.14","c_double": "3.14","c_decimal": "10.55","c_date":"'2023-10-29'","c_datetime":\"16:12:43.459\"}}"]
        schema = {
          table = "test.www"
          columns = [
            {
              name = "id"
              type = "bigint"
            },
            {
              name = "name"
              type = "string"
            },
            {
              name = "age"
              type = "int"
            }
          ]
        }
      }
    ]
  }
}

transform {
  JsonPath {
    table_match_regex = "test.a.*"
    table_transform = [{
      table_path = "test.xyz"
      columns = [
        {
          "src_field" = "name"
          "path" = "$.data.c_string"
          "dest_field" = "c2_string"
        }
      ]
    }]
    columns = [
     {
        "src_field" = "name"
        "path" = "$.data.c_string"
        "dest_field" = "c1_string"
     }
    ]
  }
}

sink {
  Assert {
    rules =
      {
        tables_configs = [
          {
            table_path = "test.abc"
            field_rules = [{
              field_name = c1_string
              field_type = string
              field_value = [
                {
                  rule_type = NOT_NULL
                }
              ]
            }]
          },
          {
            table_path = "test.xyz"
            field_rules = [{
              field_name = c2_string
              field_type = string
              field_value = [
                {
                  rule_type = NOT_NULL
                }
              ]
            }]
          },
          {
            table_path = "test.www"
            catalog_table_rule {
              table_path = "test.www"
              column_rule = [
                {
                  name = "id"
                  type = "bigint"
                },
                {
                  name = "name"
                  type = "string"
                },
                {
                  name = "age"
                  type = "int"
                }
              ]
            }
          }
        ]
      }
  }
}