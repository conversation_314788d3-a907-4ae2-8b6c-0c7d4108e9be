#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
######
###### This config file is a demonstration of streaming processing in seatunnel config
######

env {
  job.mode = "BATCH"
}

source {
  FakeSource {
    tables_configs = [
      {
        row.num = 5
        schema = {
          table = "test.abc"
          columns = [
            {
              name = "id"
              type = "bigint"
            },
            {
              name = "name"
              type = "string"
            }
          ]
        }
        rows = [
          {fields = [1, "Jia Fan"], kind = INSERT}
          {fields = [2, "Hailin Wang"], kind = INSERT}
          {fields = [3, "Tomas"], kind = INSERT}
          {fields = [4, "<PERSON>"], kind = INSERT}
          {fields = [5, "Guangdong Liu"], kind = INSERT}
        ]
      },
      {
        row.num = 5
        schema = {
          table = "test.xyz"
          columns = [
            {
              name = "id"
              type = "bigint"
            },
            {
              name = "name"
              type = "string"
            }
          ]
        }
        rows = [
          {fields = [1, "Jia Fan"], kind = INSERT}
          {fields = [2, "Hailin Wang"], kind = INSERT}
          {fields = [3, "Tomas"], kind = INSERT}
          {fields = [4, "Eric"], kind = INSERT}
          {fields = [5, "Guangdong Liu"], kind = INSERT}
        ]
      },
      {
        row.num = 5
        schema = {
          table = "test.www"
          columns = [
            {
              name = "id"
              type = "bigint"
            },
            {
              name = "name"
              type = "string"
            }
          ]
        }
        rows = [
          {fields = [1, "Jia Fan"], kind = INSERT}
          {fields = [2, "Hailin Wang"], kind = INSERT}
          {fields = [3, "Tomas"], kind = INSERT}
          {fields = [4, "Eric"], kind = INSERT}
          {fields = [5, "Guangdong Liu"], kind = INSERT}
        ]
      }
    ]
    plugin_output = "fake"
  }
}

transform {
  LLM {
    plugin_input = "fake"
    // match test.abc
    table_match_regex = "test.a.*"
    model_provider = OPENAI
    model = gpt-4o-mini
    api_key = sk-xxx
    prompt = "Determine whether someone is Chinese or American by their name"
    openai.api_path = "http://mockserver:1080/v1/chat/completions"
    table_transform = [{
      table_path = "test.xyz"
      model_provider = OPENAI
      model = gpt-4o-mini
      api_key = sk-xxx
      prompt = "Determine whether someone is Chinese or American by their name"
      openai.api_path = "http://mockserver:1080/v1/chat/completions"
    }]
    plugin_output = "llm_output"
  }
}

sink {
  Assert {
    rules =
      {
        tables_configs = [
          {
            table_path = "test.abc"
            field_rules = [{
              field_name = llm_output
              field_type = string
              field_value = [
                {
                  rule_type = NOT_NULL
                }
              ]
            }]
          },
          {
            table_path = "test.xyz"
            field_rules = [{
              field_name = llm_output
              field_type = string
              field_value = [
                {
                  rule_type = NOT_NULL
                }
              ]
            }]
          },
          {
            table_path = "test.www"
            catalog_table_rule {
              table_path = "test.www"
              column_rule = [
                {
                  name = "id"
                  type = "bigint"
                },
                {
                  name = "name"
                  type = "string"
                }
              ]
            }
          }
        ]
      }
  }
}