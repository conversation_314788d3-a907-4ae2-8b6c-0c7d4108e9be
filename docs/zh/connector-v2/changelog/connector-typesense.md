<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[Fix] Fix error log name for SourceSplitEnumerator implements class (#8817)|https://github.com/apache/seatunnel/commit/55ed90eca|2.3.10|
|[Improve] restruct connector common options (#8634)|https://github.com/apache/seatunnel/commit/f3499a6ee|2.3.10|
|[Feature]Check Chinese comments in the code (#8319)|https://github.com/apache/seatunnel/commit/d58fce1ca|2.3.9|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef80001|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03|2.3.9|
|[Fix][Connector-V2] Fix known directory create and delete ignore issues (#7700)|https://github.com/apache/seatunnel/commit/e2fb67957|2.3.8|
|[Feature][Connector-V2] Support typesense connector (#7450)|https://github.com/apache/seatunnel/commit/138d2a4eb|2.3.8|

</details>
