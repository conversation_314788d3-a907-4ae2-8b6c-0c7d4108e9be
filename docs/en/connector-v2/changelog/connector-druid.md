<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[Improve] restruct connector common options (#8634)|https://github.com/apache/seatunnel/commit/f3499a6ee|2.3.10|
|[improve] update Druid connector config option (#8594)|https://github.com/apache/seatunnel/commit/07a2288a2|2.3.10|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef80001|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03|2.3.9|
|[Improve][Connector] Add multi-table sink option check (#7360)|https://github.com/apache/seatunnel/commit/2489f6446|2.3.7|
|[Feature][Core] Support using upstream table placeholders in sink options and auto replacement (#7131)|https://github.com/apache/seatunnel/commit/c4ca74122|2.3.6|
|[Feature][Druid]Support multi table for druid sink (#7023)|https://github.com/apache/seatunnel/commit/476d49216|2.3.6|
|[Feature][Connector] Add druid sink connector (#6346)|https://github.com/apache/seatunnel/commit/d7fa9afdf|2.3.6|

</details>
