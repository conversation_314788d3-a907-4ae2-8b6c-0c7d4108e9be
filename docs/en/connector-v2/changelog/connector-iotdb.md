<details><summary> Change Log </summary>

| Change | Commit | Version |
| --- | --- | --- |
|[improve] iotdb options (#8965)|https://github.com/apache/seatunnel/commit/6e073935f|2.3.10|
|[Improve] restruct connector common options (#8634)|https://github.com/apache/seatunnel/commit/f3499a6ee|2.3.10|
|[Improve][dist]add shade check rule (#8136)|https://github.com/apache/seatunnel/commit/51ef80001|2.3.9|
|[Feature][Restapi] Allow metrics information to be associated to logical plan nodes (#7786)|https://github.com/apache/seatunnel/commit/6b7c53d03|2.3.9|
|[Improve][Common] Introduce new error define rule (#5793)|https://github.com/apache/seatunnel/commit/9d1b2582b|2.3.4|
|[Improve] Remove use `SeaTunnelSink::getConsumedType` method and mark it as deprecated (#5755)|https://github.com/apache/seatunnel/commit/8de740810|2.3.4|
|Support config column/primaryKey/constraintKey in schema (#5564)|https://github.com/apache/seatunnel/commit/eac76b4e5|2.3.4|
|[Doc] update iotdb document (#5404)|https://github.com/apache/seatunnel/commit/856aedb3c|2.3.4|
|[Improve] [Connector-V2] Remove scheduler in IoTDB sink (#5270)|https://github.com/apache/seatunnel/commit/299637868|2.3.4|
|[Hotfix] Fix com.google.common.base.Preconditions to seatunnel shade one (#5284)|https://github.com/apache/seatunnel/commit/ed5eadcf7|2.3.3|
|Merge branch &#x27;dev&#x27; into merge/cdc|https://github.com/apache/seatunnel/commit/4324ee191|2.3.1|
|[Improve][Project] Code format with spotless plugin.|https://github.com/apache/seatunnel/commit/423b58303|2.3.1|
|[improve][api] Refactoring schema parse (#4157)|https://github.com/apache/seatunnel/commit/b2f573a13|2.3.1|
|[Improve][build] Give the maven module a human readable name (#4114)|https://github.com/apache/seatunnel/commit/d7cd60105|2.3.1|
|[Improve][Project] Code format with spotless plugin. (#4101)|https://github.com/apache/seatunnel/commit/a2ab16656|2.3.1|
|[Improve][SourceConnector] Unified schema parameter, update IoTDB sou… (#3896)|https://github.com/apache/seatunnel/commit/a0959c5fd|2.3.1|
|[Feature][Connector] add get source method to all source connector (#3846)|https://github.com/apache/seatunnel/commit/417178fb8|2.3.1|
|[Feature][API &amp; Connector &amp; Doc] add parallelism and column projection interface (#3829)|https://github.com/apache/seatunnel/commit/b9164b8ba|2.3.1|
|[Hotfix][OptionRule] Fix option rule about all connectors (#3592)|https://github.com/apache/seatunnel/commit/226dc6a11|2.3.0|
|[Improve][Connector-V2][Iotdb] Unified exception for iotdb source &amp; sink connector (#3557)|https://github.com/apache/seatunnel/commit/7353fed6d|2.3.0|
|[Feature][Connector V2] expose configurable options in IoTDB (#3387)|https://github.com/apache/seatunnel/commit/06359ea76|2.3.0|
|[Improve][Connector-V2][IotDB]Add IotDB sink parameter check (#3412)|https://github.com/apache/seatunnel/commit/91240a3dc|2.3.0|
|[Bug][Connector-v2] Fix IoTDB connector sink NPE (#3080)|https://github.com/apache/seatunnel/commit/e5edf0243|2.3.0-beta|
|[Imporve][Connector-V2] Imporve iotdb connector (#2917)|https://github.com/apache/seatunnel/commit/3da11ce19|2.3.0-beta|
|[DEV][Api] Replace SeaTunnelContext with JobContext and remove singleton pattern (#2706)|https://github.com/apache/seatunnel/commit/cbf82f755|2.2.0-beta|
|[#2606]Dependency management split (#2630)|https://github.com/apache/seatunnel/commit/fc047be69|2.2.0-beta|
|[chore][connector-common] Rename SeatunnelSchema to SeaTunnelSchema (#2538)|https://github.com/apache/seatunnel/commit/7dc2a2738|2.2.0-beta|
|[Connectors-V2]Support IoTDB Source (#2431)|https://github.com/apache/seatunnel/commit/7b78d6c92|2.2.0-beta|
|[Feature][Connector-V2] Support IoTDB sink (#2407)|https://github.com/apache/seatunnel/commit/c1bbbd59d|2.2.0-beta|

</details>
