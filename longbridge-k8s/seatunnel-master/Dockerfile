FROM docker.longbridge-inc.com/long-bridge-bi/seatunnel-base:1.0.0
ARG OSS_BUCKET

ENV DOCKER true
ENV TZ Asia/Shanghai
ENV DOLPHINSCHEDULER_HOME /opt/dolphinscheduler

WORKDIR $DOLPHINSCHEDULER_HOME
# Install ossutil
RUN apt-get update &&  apt-get install -y curl unzip sudo
RUN sudo -v ; curl https://gosspublic.alicdn.com/ossutil/install.sh | sudo bash

COPY ./longbridge-k8s/.ossutilconfig /root/.ossutilconfig

RUN ossutil64 cp -r -f oss://$OSS_BUCKET/dolphinscheduler/deploy/master-server/ $DOLPHINSCHEDULER_HOME > log.txt

EXPOSE 5678 5679

CMD [ "/bin/bash", "./bin/start.sh" ]

