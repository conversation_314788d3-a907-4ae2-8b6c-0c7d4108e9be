FROM docker.longbridge-inc.com/long-bridge-bi/base-image/openjdk:8 as builder

ARG VERSION
ARG OSS_BUCKET
COPY ./seatunnel-dist/target/apache-seatunnel-${VERSION}-bin.tar.gz /opt/
#ADD https://${OSS_BUCKET}.oss-cn-hongkong.aliyuncs.com/seatunnel/deploy/apache-seatunnel-${VERSION}-bin.tar.gz /opt/
RUN cd /opt && \
    tar -zxvf apache-seatunnel-${VERSION}-bin.tar.gz && \
    mv apache-seatunnel-${VERSION} seatunnel && \
    #rm apache-seatunnel-${VERSION}-bin.tar.gz && \
    mv apache-seatunnel-${VERSION}-bin.tar.gz apache-seatunnel-bin.tar.gz && \
    sed -i 's/#rootLogger.appenderRef.consoleStdout.ref/rootLogger.appenderRef.consoleStdout.ref/' seatunnel/config/log4j2.properties && \
    sed -i 's/#rootLogger.appenderRef.consoleStderr.ref/rootLogger.appenderRef.consoleStderr.ref/' seatunnel/config/log4j2.properties && \
    sed -i 's/rootLogger.appenderRef.file.ref/#rootLogger.appenderRef.file.ref/' seatunnel/config/log4j2.properties && \
    cp seatunnel/config/hazelcast-master.yaml seatunnel/config/hazelcast-worker.yaml
ADD https://${OSS_BUCKET}.oss-cn-hongkong.aliyuncs.com/seatunnel/deploy/mario-shade-1.0.0-RELEASE.jar /opt/seatunnel/lib/

FROM docker.longbridge-inc.com/long-bridge-bi/base-image/openjdk:8
COPY --from=builder /opt/seatunnel /opt/seatunnel
COPY --from=builder /opt/apache-seatunnel-bin.tar.gz /opt/apache-seatunnel-bin.tar.gz
WORKDIR /opt/seatunnel
