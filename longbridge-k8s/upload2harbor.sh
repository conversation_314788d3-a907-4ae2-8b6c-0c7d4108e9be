#!/bin/bash

# 设置脚本在发生错误时立即退出
set -e

# 配置变量
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"  # 获取项目根目录的绝对路径
MVNW="./mvnw"
MVN="mvn"
SEATUNNEL_MODULE="seatunnel-dist"
SEATUNNEL_VERSION="2.3.11-SNAPSHOT"
SEATUNNEL_BASE_VERSION="hk-2.3.10.5"
PACKAGE_PATH="seatunnel-dist/target/apache-seatunnel-${SEATUNNEL_VERSION}-bin.tar.gz"
BASE_DOCKER_PATH="longbridge-k8s/seatunnel-base/Dockerfile"
REPOSITORY="docker.longbridge-inc.com"
OSS_BUCKET="canary-lb-bi-data-warehouse"

# 检查必要工具是否可用
check_dependencies() {
    if ! command -v "$MVNW" &> /dev/null; then
        echo "Error: mvnw not found. Please ensure mvnw is in your PATH."
        exit 1
    fi
}

# 构建模块
build_module() {
    echo "=== 开始格式化代码格式..."
    "$MVNW" spotless:apply

    echo "=== 清理并编译..."
    "$MVN" clean package -pl "$SEATUNNEL_MODULE" -am -Dmaven.test.skip=true -U
}

# 上传镜像
upload_module() {
    local local_file="$1"
    local file_name=$(basename "$local_file")
    echo "=== 登陆 docker harbor..."
    docker login docker.longbridge-inc.com -u <EMAIL> -p YFas0adAoDj3QaKbaVW4eol0fhSVLFaZ
    echo "=== 开始编译 docker镜像..."
    docker build -f $BASE_DOCKER_PATH -t $REPOSITORY/long-bridge-bi/seatunnel-base:$SEATUNNEL_BASE_VERSION --build-arg VERSION=$SEATUNNEL_VERSION --build-arg OSS_BUCKET=$OSS_BUCKET --platform linux/amd64 .
    echo "=== 登陆上传 dockerdocker镜像..."
    docker push $REPOSITORY/long-bridge-bi/seatunnel-base:$SEATUNNEL_BASE_VERSION
    echo "=== 上传成功!"
}

# 主流程
main() {
    # 切换到项目根目录
    cd "$PROJECT_ROOT"

    check_dependencies

    # 用户交互
    while true; do
        read -p "是否需要重新打包 (y/n): " package_flag
        package_flag=$(echo "$package_flag" | tr '[:upper:]' '[:lower:]')  # 使用tr命令转换为小写

        if [[ "$package_flag" == "y" || "$package_flag" == "n" ]]; then
            break
        else
            echo "请输入 y 或 n"
        fi
    done

    if [[ "$package_flag" == "y" ]]; then
        build_module
    fi

    echo "=== 开始打包镜像..."
    upload_module "$PACKAGE_PATH"
}

# 执行主流程
main
