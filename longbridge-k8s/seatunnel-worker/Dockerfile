#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

FROM docker.longbridge-inc.com/long-bridge-bi/dolphinscheduler-worker/worker-base-image:0.0.1
ARG OSS_BUCKET

ENV DOCKER true
ENV TZ Asia/Shanghai
ENV DOLPHINSCHEDULER_HOME /opt/dolphinscheduler

RUN pip install --no-cache-dir awscli

COPY ./longbridge-k8s/credentials /root/.aws/credentials
COPY ./longbridge-k8s/.ossutilconfig /root/.ossutilconfig
COPY ./longbridge-k8s/dolphinscheduler-worker/etc /opt/etc
COPY ./longbridge-k8s/dolphinscheduler-worker/etc/spark/conf/* /etc/taihao-apps/spark-conf/
WORKDIR $DOLPHINSCHEDULER_HOME

RUN ossutil64 cp -r -f oss://$OSS_BUCKET/dolphinscheduler/deploy/worker-server/ $DOLPHINSCHEDULER_HOME > log.txt

EXPOSE 1235

CMD [ "/bin/bash", "./bin/start.sh" ]
