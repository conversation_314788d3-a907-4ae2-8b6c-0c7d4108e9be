/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.hive.utils;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.connectors.seatunnel.file.config.BaseSourceConfigOptions;

import lombok.experimental.UtilityClass;

@UtilityClass
public class HiveMetaStoreProxyUtils {

    public boolean enableKerberos(ReadonlyConfig config) {
        boolean kerberosPrincipalEmpty =
                config.getOptional(BaseSourceConfigOptions.KERBEROS_PRINCIPAL).isPresent();
        boolean kerberosKeytabPathEmpty =
                config.getOptional(BaseSourceConfigOptions.KERBEROS_KEYTAB_PATH).isPresent();
        if (kerberosKeytabPathEmpty && kerberosPrincipalEmpty) {
            return true;
        }
        if (!kerberosPrincipalEmpty && !kerberosKeytabPathEmpty) {
            return false;
        }
        if (kerberosPrincipalEmpty) {
            throw new IllegalArgumentException("Please set kerberosPrincipal");
        }
        throw new IllegalArgumentException("Please set kerberosKeytabPath");
    }

    public boolean enableRemoteUser(ReadonlyConfig config) {
        return config.getOptional(BaseSourceConfigOptions.REMOTE_USER).isPresent();
    }
}
