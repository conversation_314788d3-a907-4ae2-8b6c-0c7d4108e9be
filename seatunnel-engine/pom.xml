<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at
       http://www.apache.org/licenses/LICENSE-2.0
    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel</artifactId>
        <version>2.3.11-SNAPSHOT</version>
    </parent>
    <artifactId>seatunnel-engine</artifactId>
    <packaging>pom</packaging>
    <name>SeaTunnel : Engine :</name>

    <modules>
        <module>seatunnel-engine-client</module>
        <module>seatunnel-engine-common</module>
        <module>seatunnel-engine-server</module>
        <module>seatunnel-engine-core</module>
        <module>seatunnel-engine-storage</module>
        <module>seatunnel-engine-serializer</module>
        <module>seatunnel-engine-ui</module>
    </modules>

    <properties>
        <!--  SeaTunnel Engine use     -->
        <disruptor.version>3.4.4</disruptor.version>
        <oshi.version>6.6.5</oshi.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- SeaTunnel engine use begin -->
            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>seatunnel-hazelcast-shade</artifactId>
                <version>${project.version}</version>
                <classifier>optional</classifier>
            </dependency>
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>
            <!-- SeaTunnel engine use end -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
